<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TAR Daily Report - {{ $dailyReport->unit->unit_code ?? 'N/A' }}</title>
    <style>
        @page {
            margin: 0;
            padding: 0;
            size: A4 portrait;
        }

        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            font-size: 10px;
            line-height: 1.2;
            font-weight: bold;
            color: #000;
            position: relative;
        }

        .template-background {
            position: absolute;
            top: 0;
            left: 0;
            width: 210mm;
            height: 297mm;
            z-index: 1;
        }

        .template-background img {
            width: 100%;
            height: 100%;
            object-fit: fill;
        }

        .content-overlay {
            position: relative;
            z-index: 2;
            width: 210mm;
            height: 297mm;
            padding: 0;
            margin: 0;
        }

        /* Header Right Form Fields */
        .header-form {
            position: absolute;
            top: 18.5mm;
            right: 15mm;
        }

        .form-field {
            font-size: 12px;
            margin-bottom: 2mm;
            color: #000;

        }

        /* Data Unit Fields */
        .data-fields {
            position: absolute;
            top: 36mm;
            left: 22mm;
            right: 15mm;
        }

        .field-left {
            position: absolute;
            left: 0;
            width: 85mm;
        }

        .field-right {
            position: absolute;
            right: 0;
            width: 100mm;
        }

        .field-item {
            font-size: 10px;
            font-weight: bold;
            color: #000;
        }

        .field-value {
            display: inline-block;
            margin-left: 2mm;
            font-weight: normal;
        }

        /* Problem Issue Section */
        .problem-section {
            position: absolute;
            top: 145mm;
            left: 15mm;
            right: 15mm;
            height: 25mm;
        }

        .problem-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }

        /* Parts Failure Analysis Section */
        .analysis-section {
            position: absolute;
            top: 175mm;
            left: 15mm;
            right: 15mm;
            height: 30mm;
        }

        .analysis-content {
            font-size: 9px;
            color: #000;
            padding: 2mm;
            line-height: 1.3;
        }


        .parts-table {
            width: 100%;
            border-collapse: collapse;
        }

        .parts-table td {
            text-align: center;
            justify-content: center;
            align-items: center;
            color: #000;
            font-size: 8px;
        }

        /* Picture Component Failure Section */
        .picture-section {
            top: 138mm;
            left: 15mm;
        }

        .picture-label {
            font-size: 6px;
            font-weight: bold;
            text-align: center;
            margin-bottom: 1mm;
            color: #000;
        }

        .picture-content img {
            border: 1px solid #ccc;
        }

        /* Correction Action Section */
        .correction-section {
            position: absolute;
            top: 265mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .correction-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        /* Recommendation Section */
        .recommendation-section {
            position: absolute;
            top: 280mm;
            left: 15mm;
            right: 15mm;
            height: 12mm;
        }

        .recommendation-content {
            font-size: 8px;
            color: #000;
            padding: 1mm;
            line-height: 1.2;
        }

        table td {
            border: rgba(155, 252, 0, 0.41) solid 2.4px;
        }
    </style>
</head>

<body>
    <!-- Background Template Image -->
    <div class="template-background">
        <img src="{{ public_path('assets/images/tartemplate.png') }}" alt="TAR Template">
    </div>

    <!-- Content Overlay -->
    <div class="content-overlay">

        <!-- Header Form Fields (Top Right) -->
        <div class="header-form">
            <div class="form-field">PT. PUTERA WIBOWO BORNEO</div>
            <div class="form-field">AC UNIT</div>
        </div>

        <!-- Data Unit Fields -->
        <div class="data-fields">
            <table style="font-size: 10px; font-weight: bold; border:rgba(155, 252, 0, 0.42) solid 2px;">
                <tr>
                    <td style="width: 82px;height: 18px; padding: 2px; align-items: center;">{{ $dailyReport->unit->unit_code ?? '' }}</td>
                    <td style="width: 65px;height: 18px; padding: 2px;"></td>
                    <td style="width: 325px;height: 20px;"></td>
                    <td style="width: 20px;height: 20px;">{{ $dailyReport->problem_component ?? '' }}</td>
                </tr>
                <tr>
                    <td>{{ $dailyReport->unit->unit_code ?? '' }}</td>
                    <td>{{ $dailyReport->technicians[0]['name'] ?? '' }}</td>
                    <td>{{ $dailyReport->technicians[2]['name'] ?? '' }}</td>
                    <td>{{ $dailyReport->lifetimecomponent ?? '-' }}</td>
                </tr>
                <tr>
                    <td>{{ $dailyReport->hm ?? '' }}</td>
                    <td>{{ $dailyReport->technicians[1]['name'] ?? '' }}</td>
                    <td>{{ $dailyReport->technicians[3]['name'] ?? '' }}</td>
                    @php
                    $date = \Carbon\Carbon::parse($dailyReport->date_in);
                    $formattedDate = $date->format('d/m/Y');
                    @endphp
                    <td>{{ $formattedDate }}</td>
                </tr>
            </table>
        </div>
        <div style="left: 20mm; top: 63mm; position: absolute;">
            <table>
                <tr>
                    <td>{{ $dailyReport->problem_description ?? '' }}</td>
                </tr>
            </table>
        </div>

        <div style="left: 15mm; top: 77mm; position: absolute;">
            <table>
                <tr>
                    <td>{{ $dailyReport->problem_component ?? '' }}</td>
                </tr>
            </table>
        </div>

        <!-- Main Parts Problem Table -->
        <div style=" position: absolute;
            top: 110.5mm;
            left: 5mm;
            right: 15mm;
            height: 20mm;">
            <table class="parts-table">
                <tbody>
                    <!-- looping the main part problem -->
                    <tr>
                        <td style="width: 22%;height: 13px;">part name</td>
                        <td style="width: 32%;">part number</td>
                        <td style="width: 12%;">1 EA</td>
                        <td style="width: 40%;">remaks</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Picture Component Failure Section -->
        <div class="picture-section">
            <div style="top: 140mm; left: 10mm; position: absolute; width: 100%; align-items: center; text-align: center;">
                <table style="align-items: center; justify-content: center;">
                    <tr>
                        @if($dailyReport->images->count() > 0)
                        @foreach($dailyReport->images->take(4) as $image)
                        <td style="max-width: 200px; padding-right: 50px; align-items: flex-start;">
                            <div>
                                <div style="text-align: center; font-size: 16px;">
                                    @if($image->type == 'before')
                                    Gambar Sebelum
                                    @elseif($image->type == 'after')
                                    Gambar Sesudah
                                    @elseif($image->type == 'unit')
                                    Gambar Unit
                                    @else
                                    Gambar
                                    @endif
                                </div>
                                <img style="width: 200px; height: 270px;" src="{{ public_path('assets/daily_reports/' . $image->image_path) }}" alt="Component Image">
                            </div>
                        </td>
                        @endforeach
                        @endif
                    </tr>
                </table>
            </div>
        </div>

        <!-- Plan Fix Section -->
        <table style="top: 233mm; left: 10mm; position: absolute;font-size: 12px;">
            <tr>
                <td style="width: 100%; height: 10px;">{{ $dailyReport->plan_fix ?? '' }}</td>
            </tr>
        </table>

        <!-- Plan Rekomen Section -->
        <table style="top: 252mm; left: 10mm; position: absolute;font-size: 12px;">
            <tr>
                <td style="width: 100%; height: 10px;">{{ $dailyReport->plan_rekomen ?? '' }}</td>
            </tr>
        </table>

        <!-- Lifetime Component Section -->
        <table style="top: 270mm; left: 10mm; position: absolute;font-size: 12px;">
            <tr>
                <td style="width: 100%; height: 10px;">{{ $dailyReport->lifetime_component ?? '' }}</td>
            </tr>
        </table>

        <!-- Part Problems Section -->
        @if($dailyReport->partProblems && $dailyReport->partProblems->count() > 0)
        <div style="position: absolute; top: 160mm; left: 15mm; right: 15mm;">
            <table style="width: 100%; border-collapse: collapse; font-size: 8px;">
                <thead>
                    <tr style="background-color: #f0f0f0;">
                        <th style="border: 1px solid #000; padding: 2px; text-align: center;">Kode Part</th>
                        <th style="border: 1px solid #000; padding: 2px; text-align: center;">Nama Part</th>
                        <th style="border: 1px solid #000; padding: 2px; text-align: center;">Qty</th>
                        <th style="border: 1px solid #000; padding: 2px; text-align: center;">Satuan</th>
                        <th style="border: 1px solid #000; padding: 2px; text-align: center;">Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($dailyReport->partProblems as $partProblem)
                    <tr>
                        <td style="border: 1px solid #000; padding: 2px;">{{ $partProblem->code_part }}</td>
                        <td style="border: 1px solid #000; padding: 2px;">{{ $partProblem->part_name }}</td>
                        <td style="border: 1px solid #000; padding: 2px; text-align: center;">{{ $partProblem->quantity }}</td>
                        <td style="border: 1px solid #000; padding: 2px; text-align: center;">{{ $partProblem->eum }}</td>
                        <td style="border: 1px solid #000; padding: 2px;">{{ $partProblem->remarks ?? '' }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif

        <!-- Correction Action Section -->
        <div class="correction-section">
            <div class="correction-content">
                @if($dailyReport->jobs->count() > 0)
                @foreach($dailyReport->jobs as $job)
                @if($job->highlight)
                {{ $job->job_description }}<br>
                @endif
                @endforeach
                @endif
            </div>
        </div>

        <!-- Recommendation Preventive Action Section -->
        <div class="recommendation-section">
            <div class="recommendation-content">
                <!-- This section can be filled manually or from additional data -->
            </div>
        </div>

    </div>
</body>

</html>